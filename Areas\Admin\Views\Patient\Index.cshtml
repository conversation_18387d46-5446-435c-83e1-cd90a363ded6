@model IEnumerable<MyMvcApp.Models.Patient>

@{
    ViewData["Title"] = "Quản lý bệnh nhân";
}

@section Styles {
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Quản lý bệnh nhân</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a asp-action="Create" class="btn btn-primary">
            <i class="fas fa-plus"></i> Thêm bệnh nhân mới
        </a>
    </div>
</div>

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="patient-table" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Mã</th>
                                    <th>Họ tên</th>
                                    <th>Ngày sinh</th>
                                    <th>Giới tính</th>
                                    <th>Số điện thoại</th>
                                    <th>Email</th>
                                    <th>Địa chỉ</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>@item.Id</td>
                                        <td>@item.FullName</td>
                                        <td>@item.DateOfBirth.ToString("dd/MM/yyyy")</td>
                                        <td>@item.Gender</td>
                                        <td>@item.PhoneNumber</td>
                                        <td>@item.Email</td>
                                        <td>@item.Address</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-primary" title="Sửa">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-info" title="Chi tiết">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger delete-patient"
                                                        data-id="@item.Id"
                                                        data-name="@item.FullName"
                                                        title="Xóa">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

<!-- Modal Xác nhận xóa -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xác nhận xóa bệnh nhân</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Bạn có chắc chắn muốn xóa bệnh nhân <strong id="deletePatientName"></strong>?</p>
                <p class="text-danger">Lưu ý: Hành động này không thể hoàn tác.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Xóa</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function () {
            // Khởi tạo DataTable
            var dataTable = $('#patient-table').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json'
                },
                order: [[0, 'desc']],
                pageLength: 25,
                responsive: true
            });

            // Xử lý sự kiện click nút xóa
            $(document).on('click', '.delete-patient', function () {
                var patientId = $(this).data('id');
                var patientName = $(this).data('name');

                $('#deletePatientName').text(patientName);
                $('#deleteModal').data('patient-id', patientId);

                var modal = new bootstrap.Modal(document.getElementById('deleteModal'));
                modal.show();
            });

            // Xử lý sự kiện xác nhận xóa
            $('#confirmDelete').click(function () {
                var patientId = $('#deleteModal').data('patient-id');

                $.ajax({
                    url: '@Url.Action("Delete")',
                    type: 'POST',
                    data: {
                        id: patientId,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function (response) {
                        if (response.success) {
                            // Xóa hàng khỏi DataTable
                            var row = dataTable.row($('button[data-id="' + patientId + '"]').closest('tr'));
                            row.remove().draw();

                            // Hiển thị thông báo thành công
                            showSuccessMessage('Đã xóa bệnh nhân thành công!');
                        } else {
                            showErrorMessage(response.errors.join('<br>'));
                        }
                    },
                    error: function (xhr, status, error) {
                        var errorMessage = 'Có lỗi xảy ra khi xóa bệnh nhân. Vui lòng thử lại.';
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.errors && response.errors.length > 0) {
                                errorMessage = response.errors.join('<br>');
                            }
                        } catch (e) {
                            console.error('Error parsing response:', e);
                        }
                        showErrorMessage(errorMessage);
                    },
                    complete: function () {
                        // Đóng modal
                        $('#deleteModal').modal('hide');
                    }
                });
            });

            // Hàm hiển thị thông báo thành công
            function showSuccessMessage(message) {
                var alert = $('<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                    message +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                    '</div>');

                $('.card-body').prepend(alert);

                // Tự động đóng thông báo sau 5 giây
                setTimeout(function() {
                    alert.alert('close');
                }, 5000);
            }

            // Hàm hiển thị thông báo lỗi
            function showErrorMessage(message) {
                var alert = $('<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                    message +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                    '</div>');

                $('.card-body').prepend(alert);

                // Tự động đóng thông báo sau 5 giây
                setTimeout(function() {
                    alert.alert('close');
                }, 5000);
            }
        });
    </script>
}