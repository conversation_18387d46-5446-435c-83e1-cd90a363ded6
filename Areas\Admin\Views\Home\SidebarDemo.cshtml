@using Microsoft.AspNetCore.Identity
@inject UserManager<ApplicationUser> UserManager

@{
    ViewData["Title"] = "Demo Sidebar - Nha Khoa 3B";
    
    var user = await UserManager.GetUserAsync(User);
    var isAdmin = user != null && await UserManager.IsInRoleAsync(user, "Admin");
    var isStaff = user != null && await UserManager.IsInRoleAsync(user, "Staff");
    var isDentist = user != null && await UserManager.IsInRoleAsync(user, "Dentist");
    
    var userRole = isAdmin ? "Admin" : (isStaff ? "Staff" : (isDentist ? "Dentist" : "User"));
    var roleIcon = isAdmin ? "fas fa-user-shield" : (isStaff ? "fas fa-user-nurse" : (isDentist ? "fas fa-user-md" : "fas fa-user"));
    var roleColor = isAdmin ? "danger" : (isStaff ? "info" : (isDentist ? "success" : "primary"));
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-sidebar me-2"></i>
                        Demo Sidebar Thống Nhất - Nha Khoa 3B
                    </h4>
                </div>
                <div class="card-body p-4">
                    <!-- Current User Info -->
                    <div class="alert alert-@roleColor alert-dismissible fade show" role="alert">
                        <h5 class="alert-heading">
                            <i class="@roleIcon me-2"></i>
                            Chào mừng, @user?.FullName!
                        </h5>
                        <p class="mb-2">
                            <strong>Role hiện tại:</strong> 
                            <span class="badge bg-@roleColor fs-6">@userRole</span>
                        </p>
                        <p class="mb-0">
                            <strong>Email:</strong> @user?.Email
                        </p>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>

                    <!-- Sidebar Features Overview -->
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-star me-2"></i>
                                Tính năng Sidebar Thống Nhất
                            </h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-3"></i>
                                    <div>
                                        <strong>Phân quyền động</strong>
                                        <br><small class="text-muted">Hiển thị menu theo role của người dùng</small>
                                    </div>
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-3"></i>
                                    <div>
                                        <strong>Giao diện hiện đại</strong>
                                        <br><small class="text-muted">Gradient, glassmorphism, animations</small>
                                    </div>
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-3"></i>
                                    <div>
                                        <strong>Responsive design</strong>
                                        <br><small class="text-muted">Hoạt động tốt trên mọi thiết bị</small>
                                    </div>
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-3"></i>
                                    <div>
                                        <strong>Thống nhất</strong>
                                        <br><small class="text-muted">Một sidebar cho tất cả các role</small>
                                    </div>
                                </li>
                            </ul>
                        </div>

                        <div class="col-md-6">
                            <h5 class="text-info mb-3">
                                <i class="fas fa-users me-2"></i>
                                Quyền truy cập theo Role
                            </h5>
                            
                            <!-- Admin Features -->
                            <div class="card mb-3 border-danger">
                                <div class="card-header bg-danger text-white py-2">
                                    <small><i class="fas fa-user-shield me-2"></i>Admin</small>
                                </div>
                                <div class="card-body py-2">
                                    <small>
                                        ✅ Dashboard, Lịch hẹn, Bệnh nhân<br>
                                        ✅ Dịch vụ, Thanh toán<br>
                                        ✅ Quản lý người dùng, Báo cáo<br>
                                        ✅ Dữ liệu test
                                    </small>
                                </div>
                            </div>

                            <!-- Staff Features -->
                            <div class="card mb-3 border-info">
                                <div class="card-header bg-info text-white py-2">
                                    <small><i class="fas fa-user-nurse me-2"></i>Staff</small>
                                </div>
                                <div class="card-body py-2">
                                    <small>
                                        ✅ Dashboard, Lịch hẹn, Bệnh nhân<br>
                                        ✅ Dịch vụ, Thanh toán<br>
                                        ❌ Quản lý người dùng, Báo cáo<br>
                                        ❌ Dữ liệu test
                                    </small>
                                </div>
                            </div>

                            <!-- Dentist Features -->
                            <div class="card mb-3 border-success">
                                <div class="card-header bg-success text-white py-2">
                                    <small><i class="fas fa-user-md me-2"></i>Dentist</small>
                                </div>
                                <div class="card-body py-2">
                                    <small>
                                        ✅ Dashboard, Lịch hẹn, Bệnh nhân<br>
                                        ❌ Dịch vụ, Thanh toán<br>
                                        ❌ Quản lý người dùng, Báo cáo<br>
                                        ❌ Dữ liệu test
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="text-secondary mb-3">
                                <i class="fas fa-tools me-2"></i>
                                Thao tác nhanh
                            </h5>
                            <div class="btn-group-vertical d-grid gap-2 d-md-block">
                                <a href="@Url.Action("Index", "Home", new { area = "Admin" })" class="btn btn-primary">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    Dashboard
                                </a>
                                <a href="@Url.Action("Index", "Appointment", new { area = "Admin" })" class="btn btn-success">
                                    <i class="fas fa-calendar-check me-2"></i>
                                    Quản lý lịch hẹn
                                </a>
                                <a href="@Url.Action("Index", "Patient", new { area = "Admin" })" class="btn btn-info">
                                    <i class="fas fa-user-injured me-2"></i>
                                    Quản lý bệnh nhân
                                </a>
                                @if (isAdmin || isStaff)
                                {
                                    <a href="@Url.Action("Index", "Service", new { area = "Admin" })" class="btn btn-warning">
                                        <i class="fas fa-tooth me-2"></i>
                                        Quản lý dịch vụ
                                    </a>
                                }
                                @if (isAdmin)
                                {
                                    <a href="@Url.Action("Index", "UserManagement", new { area = "Admin" })" class="btn btn-danger">
                                        <i class="fas fa-users me-2"></i>
                                        Quản lý người dùng
                                    </a>
                                }
                            </div>
                        </div>
                    </div>

                    <!-- Test Accounts -->
                    @if (isAdmin)
                    {
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-warning mb-3">
                                    <i class="fas fa-user-plus me-2"></i>
                                    Tạo tài khoản test (Chỉ Admin)
                                </h5>
                                <div class="btn-group-vertical d-grid gap-2 d-md-block">
                                    <a href="@Url.Action("CreateStaff", "Home", new { area = "Admin" })" class="btn btn-outline-info">
                                        <i class="fas fa-user-nurse me-2"></i>
                                        Tạo tài khoản Staff
                                    </a>
                                    <a href="@Url.Action("CreateDentist", "Home", new { area = "Admin" })" class="btn btn-outline-success">
                                        <i class="fas fa-user-md me-2"></i>
                                        Tạo tài khoản Dentist
                                    </a>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Footer -->
                    <div class="row mt-5">
                        <div class="col-12 text-center">
                            <div class="border-top pt-3">
                                <p class="text-muted mb-2">
                                    <i class="fas fa-tooth me-2"></i>
                                    <strong>Nha Khoa 3B</strong> - Hệ thống quản lý nha khoa hiện đại
                                </p>
                                <small class="text-muted">
                                    Sidebar thống nhất cho tất cả các role: Admin, Staff, Dentist
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .bg-gradient-primary {
        background: linear-gradient(135deg, #4A9F9F 0%, #2E7D7D 100%);
    }
    
    .card {
        border-radius: 15px;
        overflow: hidden;
    }
    
    .list-group-item {
        border: none;
        padding: 0.75rem 0;
    }
    
    .btn {
        border-radius: 10px;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }
</style>
