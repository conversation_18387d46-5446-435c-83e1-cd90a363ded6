@model IEnumerable<MyMvcApp.Models.Appointment>
@{
    ViewData["Title"] = "Quản lý lịch hẹn";
}

@section Styles {
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
}


@Html.AntiForgeryToken()

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Quản lý lịch hẹn</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a asp-controller="Appointment" asp-action="Calendar" class="btn btn-info me-2">
            <i class="fas fa-calendar"></i> Xem lịch
        </a>
        <a asp-controller="Appointment" asp-action="Create" class="btn btn-primary">
            <i class="fas fa-plus"></i> Thêm lịch hẹn mới
        </a>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="appointmentTable">
                            <thead>
                                <tr>
                                    <th>Mã</th>
                                    <th>Bệnh nhân</th>
                                    <th>Dịch vụ</th>
                                    <th>Ngày hẹn</th>
                                    <th>Giờ hẹn</th>
                                    <th>Trạng thái</th>
                                    <th>Ghi chú</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>@item.Id</td>
                                        <td>@item.PatientName</td>
                                        <td>@item.Service.Name</td>
                                        <td>@item.AppointmentDate.ToString("dd/MM/yyyy")</td>
                                        <td>@item.StartTime.ToString(@"hh\:mm")</td>
                                        <td>
                                            <span class="badge @(item.Status switch {
                                                "Scheduled" => "bg-primary",
                                                "Completed" => "bg-success",
                                                "Cancelled" => "bg-danger",
                                                _ => "bg-warning"
                                            })">
                                                @item.Status
                                            </span>
                                        </td>
                                        <td>@(item.Notes?.Length > 50 ? item.Notes.Substring(0, 50) + "..." : item.Notes)</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-controller="Appointment" asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a asp-controller="Appointment" asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger"
                                                        onclick="confirmDelete('@item.Id')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
        </div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xác nhận xóa</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Bạn có chắc chắn muốn xóa lịch hẹn này không?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Xóa</button>
            </div>
        </div>

@section Scripts {
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#appointmentTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json'
                },
                order: [[0, 'desc']],
                pageLength: 25,
                responsive: true
            });
        });

        function confirmDelete(id) {
            $('#deleteModal').data('appointment-id', id).modal('show');
        }

        $('#confirmDelete').click(function () {
            var appointmentId = $('#deleteModal').data('appointment-id');

            $.ajax({
                url: '@Url.Action("Delete")',
                type: 'POST',
                data: {
                    id: appointmentId,
                    __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                },
                success: function (response) {
                    if (response.success) {
                        // Xóa hàng khỏi DataTable
                        var table = $('#appointmentTable').DataTable();
                        var row = table.row($('button[onclick="confirmDelete(\'' + appointmentId + '\')"]').closest('tr'));
                        row.remove().draw();

                        // Hiển thị thông báo thành công
                        alert('Đã xóa lịch hẹn thành công!');
                    } else {
                        alert(response.errors.join('\n'));
                    }
                },
                error: function (xhr, status, error) {
                    var errorMessage = 'Có lỗi xảy ra khi xóa lịch hẹn. Vui lòng thử lại.';
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response.errors && response.errors.length > 0) {
                            errorMessage = response.errors.join('\n');
                        }
                    } catch (e) {
                        console.error('Error parsing response:', e);
                    }
                    alert(errorMessage);
                },
                complete: function () {
                    // Đóng modal
                    $('#deleteModal').modal('hide');
                }
            });
        });
    </script>
}