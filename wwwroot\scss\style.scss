@import 'bootstrap/bootstrap';
@import 'bootstrap/variables';

$font-primary: 'Open Sans',<PERSON><PERSON>, sans-serif;

$white: #fff;
$black: #000;
// $darken: #3a4348;

$primary: #2f89fc;
$secondary: #2cbcbc;
$tertiary: #21aac4;


@mixin gradient-background(){
	background: rgba(47,137,252,1);
	background: -moz-linear-gradient(-45deg, rgba(47,137,252,1) 0%, rgba(44,188,188,1) 100%);
	background: -webkit-gradient(left top, right bottom, color-stop(0%, rgba(47,137,252,1)), color-stop(100%, rgba(44,188,188,1)));
	background: -webkit-linear-gradient(-45deg, rgba(47,137,252,1) 0%, rgba(44,188,188,1) 100%);
	background: -o-linear-gradient(-45deg, rgba(47,137,252,1) 0%, rgba(44,188,188,1) 100%);
	background: -ms-linear-gradient(-45deg, rgba(47,137,252,1) 0%, rgba(44,188,188,1) 100%);
	background: linear-gradient(135deg, rgba(47,137,252,1) 0%, rgba(44,188,188,1) 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#2f89fc', endColorstr='#00dc94', GradientType=1 );
}

@mixin gradient-background-gray(){
	background: rgba(240,240,240,1);
	background: -moz-linear-gradient(45deg, rgba(240,240,240,1) 0%, rgba(255,255,255,1) 55%, rgba(255,255,255,1) 100%);
	background: -webkit-gradient(left bottom, right top, color-stop(0%, rgba(240,240,240,1)), color-stop(55%, rgba(255,255,255,1)), color-stop(100%, rgba(255,255,255,1)));
	background: -webkit-linear-gradient(45deg, rgba(240,240,240,1) 0%, rgba(255,255,255,1) 55%, rgba(255,255,255,1) 100%);
	background: -o-linear-gradient(45deg, rgba(240,240,240,1) 0%, rgba(255,255,255,1) 55%, rgba(255,255,255,1) 100%);
	background: -ms-linear-gradient(45deg, rgba(240,240,240,1) 0%, rgba(255,255,255,1) 55%, rgba(255,255,255,1) 100%);
	background: linear-gradient(45deg, rgba(240,240,240,1) 0%, rgba(255,255,255,1) 55%, rgba(255,255,255,1) 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f0f0f0', endColorstr='#ffffff', GradientType=1 );
}
@mixin border-radius($radius) {
  -webkit-border-radius: $radius;
     -moz-border-radius: $radius;
      -ms-border-radius: $radius;
          border-radius: $radius;
}

@mixin transition($transition) {
    -moz-transition:    all $transition ease;
    -o-transition:      all $transition ease;
    -webkit-transition: all $transition ease;
    -ms-transition: 		all $transition ease;
    transition:         all $transition ease;
}

html {
	// overflow-x: hidden;
}
body {
	font-family: $font-primary;
	background: $white;
	font-size: 15px;
	line-height: 1.8;
	font-weight: 400;
	color: lighten($black,50%);
	&.menu-show {
		overflow: hidden;
		position: fixed;
		height: 100%;
		width: 100%;
	}
}
a {
	transition: .3s all ease;
	color: $primary;
	&:hover,&:focus {
		outline: none !important;
		text-decoration: none;
		color: $primary;
	}
}
h1, h2, h3, h4, h5,
.h1, .h2, .h3, .h4, .h5 {
	line-height: 1.5;
	color: $black;
	font-weight: 400;
}

.text-primary {
	color: $primary!important;
}

.ftco-navbar-light {
	background: transparent!important;
	position: absolute;
	top: 20px;
	left: 0;
	right: 0;
	z-index: 3;
	@include media-breakpoint-up(lg){
		padding: 15px 0;
	}
	@include media-breakpoint-down(md) {
		background: $black!important;
		position: relative;
		top: 0;
	}

	.navbar-brand {
		color: $white;
		font-weight: 400;
		span{
			font-weight: 700;
		}
		@include media-breakpoint-up(md){
			color: $white;
		}
	}
	
	.navbar-nav {
		@include media-breakpoint-down(md){
			padding-bottom: 20px;
		}
		> .nav-item {
			> .nav-link {
				font-size: 13px;
				padding-top: .9rem;
				padding-bottom: .9rem;
				padding-left: 20px;
				padding-right: 20px;
				font-weight: 400;
				color: $white;
				@include media-breakpoint-up(lg){
					color: $white;
				}
				@include media-breakpoint-down(sm){
					padding-left: 0;
				}
				&:hover {
					color: rgba(255,255,255,.8);
				}
				opacity: 1!important;
			}

			.dropdown-menu{
				border: none;
				background: $white;
				-webkit-box-shadow: 0px 10px 34px -20px rgba(0,0,0,0.41);
				-moz-box-shadow: 0px 10px 34px -20px rgba(0,0,0,0.41);
				box-shadow: 0px 10px 34px -20px rgba(0,0,0,0.41);
			}

			
			&.ftco-seperator {
				position: relative;
				margin-left: 20px;
				padding-left: 20px;
				@include media-breakpoint-down(md) {
					padding-left: 0;
					margin-left: 0;
				}
				&:before {
					position: absolute;
					content: "";
					top: 10px;
					bottom: 10px;
					left: 0;
					width: 2px;
					background: rgba($white, .05);
					@include media-breakpoint-down(md) {
						display: none;
					}
				}
			}
			&.cta {
				> a {
					color: $black;
					border: 1px solid rgba(255,255,255,.2);
					padding-top: .5rem;
					padding-bottom: .5rem;
					padding-left: 20px;
					padding-right: 20px;
					margin-top: 4px;
					@include border-radius(30px);
					@include media-breakpoint-down(md){
						@include border-radius(4px);
					}
					span {
						display: inline-block;
						color: $white;
					}
				}
				&.cta-colored {
					span {
						border-color: $primary;
					}
				}
			}
			&.active {
				> a {
					color: rgba(255,255,255,.5);
				}
			}
		}
	}
	.navbar-toggler {
		border: none;
		color: rgba(255,255,255,.5)!important;
		cursor: pointer;
		padding-right: 0;
		text-transform: uppercase;
		font-size: 16px;
		letter-spacing: .1em;
	}
	
	&.scrolled  {
		position: fixed;
		right: 0;
		left: 0;
		top: 0;
		margin-top: -130px;
		background: $white!important;
		box-shadow: 0 0 10px 0 rgba(0,0,0,.1);
		.nav-item {
			&.active {
				> a {
					color: $primary!important;
				}
			}
			&.cta {
				> a {
					color: $white !important;
					background: $primary;
					border: none !important;
					padding-top: .5rem!important;
					padding-bottom: .5rem !important;
					padding-left: 20px;
					padding-right: 20px;
					margin-top: 6px !important;
					@include border-radius(30px);
					@include media-breakpoint-down(md){
						@include border-radius(4px);
					}
					span {
						display: inline-block;
						color: $white !important;
					}
				}
				&.cta-colored {
					span {
						border-color: $primary;
					}
				}
			}
		}

		.navbar-nav {
			@include media-breakpoint-down(md) {
				background: none;
				border-radius: 0px;
				padding-left: 0rem!important;
				padding-right: 0rem!important;
			}
		}

		.navbar-nav {
			@include media-breakpoint-down(sm) {
				background: none;
				padding-left: 0!important;
				padding-right: 0!important;
			}
		}

		.navbar-toggler {
			border: none;
			color: rgba(0,0,0,.5)!important;
			border-color: rgba(0,0,0,.5)!important;
			cursor: pointer;
			padding-right: 0;
			text-transform: uppercase;
			font-size: 16px;
			letter-spacing: .1em;

		}
		.nav-link {
			padding-top: .9rem!important;
			padding-bottom: .9rem!important;
			color: $black!important;
			&.active {
				color: $primary!important;
			}
		}
		&.awake {
			margin-top: 0px;
			transition: .3s all ease-out;
		}
		&.sleep {
			transition: .3s all ease-out;	
		}

		.navbar-brand {
			color: $black;
		}
	}
}

.navbar-brand {
	font-weight: 700;
	line-height: 1;
	font-size: 20px;
	text-transform: uppercase;
}


//OWL CAROUSEL
.owl-carousel {
	position: relative;
	.owl-item {
		opacity: .4;
		&.active {
			opacity: 1;
		}
	}
	
	.owl-nav {
		position: absolute;
		top: 50%;
		width: 100%;
		.owl-prev,
		.owl-next {
			position: absolute;
			transform: translateY(-50%);
			margin-top: -10px;
			@include transition(.7s);
			span {
				&:before {
					font-size: 40px;
					color: lighten($black,90%);
				}
			}
			opacity: 0;
		}
		.owl-prev {
			left: 0;
		}
		.owl-next {
			right: 0;
		}
	}
	.owl-dots {
		text-align: center;
		.owl-dot {
			width: 10px;
			height: 10px;
			margin: 5px;
			// border-radius: 50%;
			transform: rotate(45deg);
			// background: lighten($black, 90%);
			border: 1px solid lighten($black, 80%);
			&.active,&:focus,&:hover {
				background: $primary;
				border: 1px solid $primary;
				outline: none !important;
			}
		}
	}
	&:hover,&:focus{
		.owl-nav{
			.owl-prev,
			.owl-next{
				opacity: 1;
				span{
					&:before{
						color: lighten($black,85%);
					}
				}
			}
			.owl-prev {
				left: -20px;
			}
			.owl-next {
				right: -20px;
			}
		}
	}
	&.home-slider {
		z-index: 0;
		position: relative;

		.slider-item {
			background-size: cover;
			background-repeat: no-repeat;
			background-position: center center;
			height: 700px;
			position: relative;
			.overlay{
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				content: '';
				background: $black;
				opacity: .2;
			}
			.slider-text {
				color: $white;
				height: 700px;
				h1 {
					font-size: 40px;
					color: $white;
					line-height: 1.2;
					font-weight: 400;
					@include media-breakpoint-down(md) {
						font-size: 40px;
					}
				}
				p {
					font-size: 20px;
					line-height: 1.5;
					font-weight: 300;
					color: rgba(255,255,255,.8);
					strong{
						font-weight: 700;
						a{
							color: $white;
						}
					}
				}
				.breadcrumbs{
					text-transform: uppercase;
					font-size: 13px;
					letter-spacing: 1px;
					span{
						border-bottom: 2px solid rgba(255,255,255,.1);
						a{
							color: $white;
						}
					}
				}
			}
			&.bread-item{
				height: 400px !important;
				@include media-breakpoint-down(lg){
					background-position: center center !important;
				}
				.overlay{
					@include gradient-background();
				}
				.slider-text {
					height: 400px;
				}
			}
		}
		.owl-nav {
			.owl-prev,
			.owl-next {
				span {
					color: $white;
				}
			}
		}
		&:hover{
			.owl-nav{
				.owl-prev,
				.owl-next{
					opacity: 1;
				}
				.owl-prev {
					left: 20px;
				}
				.owl-next {
					right: 20px;
				}
			}
		}
		.owl-dots {
			position: absolute;
			left: 0;
			right: 0;
			bottom: 130px;
			width: 100%;
			text-align: center;

			.owl-dot {
				width: 18px !important;
				height: 18px !important;
				margin: 5px;
				border-radius: 50%;
				background: lighten($black, 90%);
				background: none;
				border: 2px solid rgba(255,255,255,.5);
				outline: none!important;
				position: relative;
				transition: .3s all ease;
				display: inline-block;
				span {
					position: absolute;
					width: 12px;
					height: 12px;
					background: rgba(255,255,255,.5 );
					border-radius: 50%!important;
					left: 50%;
					top: 50%;
					display: block;
					transform: translate(-50%, -50%);
				}	
				&.active {
					border: 2px solid rgba(255,255,255,1);
					span {	
						background: rgba(255,255,255,1);
					}	
				}
			}
		}
	}
}
.owl-custom-nav {
	float: right;
	position: relative;
	z-index: 10;
	border: 1px solid red;
	.owl-custom-prev,
	.owl-custom-next {
		padding: 10px;
		font-size: 30px;
		background: #ccc;
		line-height: 0;
		width: 60px;
		text-align: center;
		display: inline-block;
	}
} 


.bg-light {
	background: #fafafa!important;
	z-index: 0;
}

.bg-primary{
	background: $primary;
}


//BUTTON
.btn {
	cursor: pointer;
	@include border-radius(30px);
	box-shadow: none!important;
	&:hover, &:active, &:focus {
		outline: none;
	}
	&.btn-primary {
		background: $primary;
		border: 1px solid $primary;
		color: $white;
		&:hover {
			border: 1px solid $primary;
			background: transparent;
			color :$primary;
		}
		&.btn-outline-primary {
			border: 1px solid $primary;
			background: transparent;
			color :$primary;
			&:hover {
				border: 1px solid transparent;
				background: $primary;
				color :$white;
			}
		}
	}
	&.btn-outline-white {
		border-color: rgba($white, .8);
		background: none;
		@include border-radius(30px);
		border-width: 1px;
		color: $white;
		&:hover, &:focus, &:active {
			background: $white;
			border-color: $white;
			color: $primary;
		}
	}
}



.ftco-intro{
	.container{
		margin-top: -100px;
		@include media-breakpoint-up(md){
			margin-top: -100px;
		}
	}
	color: $white;
	.color-1{
		background: $secondary;
	}
	.color-2{
		background: $tertiary;
	}
	.color-3{
		// background: $primary;
		@include gradient-background();
	}
	h3{
		font-size: 20px;
		color: $white;
	}
	.phone-number{
		font-size: 18px;
	}
	.openinghours{
		display: block;
		width: 100%;
		font-size: 14px;
		span{
			width: 50%;
			display: block;
		}
	}
	.appointment-form{
		.form-group{
			position: relative;
			input, select{
				appearance: none;
				background: transparent !important;
				border-bottom: 1px solid rgba(255,255,255,.2);
				color: $white !important;
				font-size: 14px;
				padding-right: 20px !important;
				option{
					color: $white;
				}
				&::-webkit-input-placeholder { /* Chrome/Opera/Safari */
				  color: $white !important;
				}
				&::-moz-placeholder { /* Firefox 19+ */
				  color: $white !important;
				}
				&:-ms-input-placeholder { /* IE 10+ */
				  color: $white !important;
				}
				&:-moz-placeholder { /* Firefox 18- */
				  color: $white !important;
				}
			}
			.btn-primary{
				@include border-radius(0);
				border: none;
				border-bottom: 1px solid rgba(255,255,255,.4);
				padding-left: 0;
				padding-right: 0 !important;
			}
		}
		.icon {
			position: absolute;
			top: 50% !important;
			right: 0;
			font-size: 14px;
			transform: translateY(-50%);
			color: $white;
			@include media-breakpoint-down(sm) {
				right: 10px;
			}
		}
	}
}

//SERVICES
.ftco-services{
	padding-bottom: 0 !important;
}
.services{
	display: block;
	width: 100%;
	@include transition(.3s);
	.icon{
		line-height: 1.3;
		position: relative;
		width: 100px;
		height: 100px;
		background: lighten($primary,40%);
		margin: 0 auto;
		@include border-radius(50%);
		span{
			font-size:50px;
			@include gradient-background();
			-webkit-background-clip: text;
		  -webkit-text-fill-color: transparent;
		}
	}
	.media-body{
		h3{
			font-size: 20px;
		}
	}
}

.about-wrap{
	width: 100%;
	padding: 30px;
	color: rgba(255,255,255,.7);
	@include gradient-background();
	@include media-breakpoint-up(md){
		padding: 7% 25% 5% 10%;
	}
	.list-services{
		display: block;
		margin-bottom: 30px;
		.icon{
			margin-top: 20px;
			width: 60px;
			height: 60px;
			@include transition(.3s);
			position: relative;
			z-index: 0;
			&:after{
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				width: 60px;
				height: 60px;
				content: '';
				border: 1px solid rgba(255,255,255,.3);
				z-index: -1;
				transform: rotate(45deg);
				@include transition(.3s);
			}
			span{
				color: $white;
				font-size: 30px;
				// @include transition(.3s);
			}
		}
		.text{
			width: calc(100% - 60px);
			padding-left: 40px;
			h3{
				font-size: 20px;
				color: $white;
			}
		}
		&:hover, &:focus{
			.icon{
				&:after{
					transform: rotate(180deg);
				}
			}
		}
	}
}


//GALLERY
.ftco-gallery{
}
.gallery{
	display: block;
	height: 450px;
	position: relative;
	.icon{
		width: 50px;
		height: 50px;
		margin: 0 auto;
		z-index: 0;
		opacity: 0;
		position: relative;
		@include transition(.6s);
		&:after{
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			width: 50px;
			height: 50px;
			content: '';
			background: $primary;
			z-index: -1;
			@include transition(.6s);
		}
		span{
			color: $white;
		}
	}
	&:hover, &:focus{
		.icon{
			opacity: 1;
			&:after{
				transform: rotate(135deg);
			}
		}
	}	
}



.ftco-section-parallax {
	position: relative;
	.parallax-img {
		background-size: cover;
		background-repeat: no-repeat;
		background-position: center center;
		position: relative;
		padding: 4em 0;
		@include gradient-background();
	}
}

//ftco-quote
.ftco-quote{
	padding: 0;
	.choose{
		p,li{
			color: rgba(255,255,255,.8);
		}
	}
	.un-styled{
		padding: 0;
		li{
			list-style: none;
			margin-bottom: 10px;
			span{
				color: $white;
				margin-right: 10px;
			}
		}
	}
}


//STAFF
.staff{
	width: 100%;
	display: block;
	padding: 25px;
	bordeR: 1px solid lighten($black,96%);
	-webkit-box-shadow: 0px 3px 66px -24px rgba(0,0,0,0.2);
	-moz-box-shadow: 0px 3px 66px -24px rgba(0,0,0,0.2);
	box-shadow: 0px 3px 66px -24px rgba(0,0,0,0.2);
	@include border-radius(4px);
	@include transition(.3s);
	@include media-breakpoint-down(sm){
		margin-bottom: 30px;
	}
	.info{
		width: 100%;
		h3{
			font-size: 18px;
			a{
				color: $black;
			}
		}
		span.position{
			display: block;
			margin-bottom: 15px;
			font-size: 13px;
			font-weight: 400;
			color: $primary;
		}
	}
	.img{
		width: 180px;
		height: 180px;
		margin: 0 auto;
		@include border-radius(50%);
		@include transition(.3s);
	}
	.text{
		.ftco-social{
			padding: 0;
			li{
				list-style: none;
				display: inline-block;
				margin: 0 5px;
			}
		}
	}
	&:hover, &:focus{
		-webkit-box-shadow: 0px 3px 66px -24px rgba(0,0,0,0.5);
		-moz-box-shadow: 0px 3px 66px -24px rgba(0,0,0,0.5);
		box-shadow: 0px 3px 66px -24px rgba(0,0,0,0.5);
	}
}

//### .block-3
.block-3 {
	@include media-breakpoint-up(md){
		margin-bottom: 7em;
	}
	.text, .image {
		width: 100%;
		padding: 10% 7%;
		display: block;
		@include media-breakpoint-up(md) {
			width: 50%;
			padding: 10% 7%;	
		}
	}
	.text {
		.subheading {
			font-size: 13px;
			text-transform: uppercase;
			letter-spacing: .1em;
		}
		.heading {
			font-size: 30px;
			margin-bottom: 30px;
			a{
				color: $black;
			}
		}
		p {
			&:last-child {
				margin-bottom: 0;
			}
		}
	}
	.image {
		background-size: cover;
		background-position: center center;
		background-repeat: no-repeat;
		position: relative;
		&:after{
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			content: '';
			box-shadow: 20px 20px 0 0 lighten($primary,25%);
		}
		&.image-2{
			&:after{
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				content: '';
				box-shadow: -20px 20px 0 0 lighten($primary,25%);
			}
		}
		@include media-breakpoint-down(sm) {
			height: 300px;
		}
	}
}

//### .block-5
.block-5 {
	overflow: hidden;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center center;
	height: 400px;
	position: relative;
	display: block;

	&:before {
		content: '';
      position: absolute;
      top: 0;
    	right: 0;
    	bottom: 0;
   	left: 0;
    	background: -moz-linear-gradient(top, transparent 0%, transparent 18%, rgba(0,0,0,0.8) 99%, rgba(0,0,0,0.8) 100%);
    	background: -webkit-linear-gradient(top, transparent 0%, transparent 18%, rgba(0,0,0,0.8) 99%, rgba(0,0,0,0.8) 100%);
    	background: linear-gradient(to bottom, transparent 0%, transparent 18%, rgba(0,0,0,0.8) 99%, rgba(0,0,0,0.8) 100%);
    	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00000000', endColorstr='#cc000000',GradientType=0 );
    	opacity: .8;
	}
	.text {
		position: absolute;
		bottom: 0;
		right: 0;
		left: 0;
		padding: 20px 20px 10px 20px;
		transition: .8s all ease;

		&:before {
			bottom: 0;
			left: 0;
			right: 0;
			z-index: 1;
			background: $white;	
			visibility: hidden;
			opacity: 0;
			height: 0;
			position: absolute;
			content: "";
			@include transition(.3s);
		}
		.heading, .subheading, .post-meta, .excerpt, .price{
			z-index: 2;
			position: relative;
		}
		.subheading {
			color: $white;
			text-transform: uppercase;
			letter-spacing: .1em;
			font-size: 12px;
			margin-bottom: 5px;
			opacity: .6;
		}
		.heading {
			color: $white;
			margin: 0 0 10px 0;
			padding: 0;
			font-weight: bold;
			font-size: 24px;
			line-height: 1.2;
			font-weight: 400;
		}
		.post-meta {
			line-height: 1.4;
			color: $white;
			font-size: 14px;
			// text-transform: uppercase;
			span {
				display: inline-block;
				margin-right: 10px;
				margin-bottom: 10px;
				opacity: .6;

			}
		}
		.price{
			color: $white;
		}
		.excerpt {
			line-height: 1.4;
			color: $white;
		}
	}
	&:hover, &:focus {
		.text {
			
			&:before {
				visibility: visible;
				opacity: 1;
				height: 100%;
				background:$white;
			}
			.heading, .subheading, .post-meta, .price, .star-rate {
				color: $black;
			}
			
		}
	}
}

// USEFUL CODE
.aside-stretch{
	background: $primary;
	z-index: 0;
	&:after{
		position: absolute;
		top: 0;
		right: 100%;
		bottom: 0;
		content: '';
		width: 360%;
		background: $primary;
		z-index: -1;
	}
	@include media-breakpoint-down(sm){
		background: $primary;
		&:after{
			background: transparent;
			display: none;
		}
	}
}


.form-control {
	height: 52px!important;
	background: $white!important;
	color: $black!important;
	font-size: 16px;
	border-radius: 0px;
	padding-right: 0;
	padding-left: 0;
	box-shadow: none!important;
	border: none;
	border-bottom: 1px solid lighten($black,90%);
	&:focus, &:active {
		border-color: lighten($black,80%);
	}
}
textarea.form-control {
	height: inherit!important;
}
.ftco-vh-100 {
  height: 100vh;
  @include media-breakpoint-down(lg) {
  	height: inherit;
  	padding-top: 5em;
  	padding-bottom: 5em;
  }
}
.ftco-vh-75 {
  height: 75vh;
  min-height: 700px;
  @include media-breakpoint-down(lg) {
  	min-height: 700px;
  	height: inherit;
  	padding-top: 5em;
  	padding-bottom: 5em;
  }
}


.ftco-tab-nav {
	padding: 0;
	margin: 0;
	display: inline-block!important;
	@include media-breakpoint-down(sm) {
		display: block!important;
		margin-bottom: 10px;
		width: 100%!important;
	}
	li {
		padding: 0;
		margin: 0 5px;
		display: inline-block!important;
		@include media-breakpoint-down(sm) {
			display: block!important;
			margin-bottom: 10px;
			width: 100%!important;
		}
		a {
			text-transform: uppercase;
			font-size: 14px;
			letter-spacing: .2em;
			color: #ccc;
			border: 2px solid #ccc;
			border-radius: 0!important;
			&.active {
				background: none!important;
				color: darken(#ccc, 100%)!important;
				border: 2px solid $black;
			}
		}
		
	}
}

.ftco-animate {
	opacity: 0;
	visibility: hidden;
}

.bg-primary {
	background: $primary!important;
}



//ABOUT
.media-custom{
	background: $white;
	.media-body{
		.name{
			font-weight: 500;
			font-size: 16px;
			margin-bottom: 0;
			color: $primary;
		}
		.position{
			font-size: 13px;
			color: lighten($black, 85%);
		}
	}
}


.about-author{
	img{
		@include border-radius(50%);
	}
	.desc{
		h3{
			font-size: 20px;
		}
	}
	.bio{

	}
}


.ftco-section {
	padding: 7em 0;
	position: relative;
	&.ftco-section-2{
		position: relative;
		padding: 3em 0 !important;
	}
	.overlay{
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		content: '';
		opacity: 1;
		// background: $black;
		@include gradient-background();
	}
	@include media-breakpoint-down(sm){
		padding: 6em 0;
	}
}

.ftco-bg-dark{
	background: #3c312e;
}


.ftco-footer {
	font-size: 16px;
	background: #191919;
	padding: 6em 0;
	.ftco-footer-logo {
		text-transform: uppercase;
		letter-spacing: .1em;
	}
	.ftco-footer-widget {
		h2 {
			font-weight: normal;
			color: $white;
			margin-bottom: 40px;
			font-size: 18px;
			font-weight: 400;
		}
		ul{
			li{
				a{
					span{
						color: $white;
					}
				}
			}
		}
		.btn-primary{
			background: $white !important;
			border: 2px solid $white !important;
			&:hover{
				background: $white;
				border: 2px solid $white !important;
			}
		}
	}
	p {
		color: rgba($white, .7);
	}
	a {
		color: rgba($white, .7);
		&:hover {
			color: $white;
		}
	}
	.ftco-heading-2 {
		font-size: 17px;
		font-weight: 400;
		color: $black;
	}
	.block-21 {
		.text {
			.heading {
				font-size: 16px;
				font-weight: 300;
				a {
					color: rgba(255,255,255,.9);
					&:hover, &:active, &:focus {
						color: $white;
					}
				}
			}
			.meta {
				> div {
					display: inline-block;
					font-size: 12px;
					margin-right: 5px;
					a {
						color: rgba(255,255,255,.5);
					}
				}
			}
		}
	}
}


.ftco-footer-social {
	li {
		list-style: none;
		margin: 0 10px 0 0;
		display: inline-block;
		a {
			height: 50px;
			width: 50px;
			display: block;
			float: left;
			background: rgba($white, .05);
			border-radius: 50%;
			position: relative;
			span {
				position: absolute;
				font-size: 26px;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
			}
			&:hover {
				color: $white;
			}
		}
	}
}
.footer-small-nav {
	> li {
		display: inline-block;
		a {
			margin: 0 10px 10px 0;
			&:hover, &:focus {
				color: $primary;
			}
		}
	}
}
.media {
	.ftco-icon {
		width: 100px;
		span {
			color: $primary;
		}
	}
}
.ftco-media {
	background: $white;
	border-radius: 0px;
	.heading {
		font-weight: normal;
	}
	&.ftco-media-shadow {
		padding: 40px;
		background: $white;
		box-shadow: 0 10px 50px -15px rgba(0,0,0,.3);
		transition: .2s all ease;
		position: relative;
		top: 0;
		&:hover, &:focus {
			top: -3px;
			box-shadow: 0 10px 70px -15px rgba(0,0,0,.3);
		}
	}
	.icon {
		font-size: 50px;
		display: block;
		color: $primary;
	}
	&.text-center {
		.ftco-icon {
			margin: 0 auto;
		}
	}
}
.ftco-overflow-hidden {
	overflow: hidden;
}

.padding-top-bottom {
	padding-top: 120px;
	padding-bottom: 120px;
}

// Map

#map {
	height: 400px;
	width: 100%;
	@include media-breakpoint-down(md) {
		height: 300px;
	}
}


@-webkit-keyframes pulse {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba($primary, 0.4);
  }
  70% {
      -webkit-box-shadow: 0 0 0 30px rgba($primary, 0);
  }
  100% {
      -webkit-box-shadow: 0 0 0 0 rgba($primary, 0);
  }
}
@keyframes pulse {
  0% {
    -moz-box-shadow: 0 0 0 0 rgba($primary, 0.4);
    box-shadow: 0 0 0 0 rgba($primary, 0.4);
  }
  70% {
      -moz-box-shadow: 0 0 0 30px rgba($primary, 0);
      box-shadow: 0 0 0 30px rgba($primary, 0);
  }
  100% {
      -moz-box-shadow: 0 0 0 0 rgba($primary, 0);
      box-shadow: 0 0 0 0 rgba($primary, 0);
  }
}

.heading-section{
	.subheading{
		font-size: 14px;
		font-size: 400;
		display: block;
		margin-bottom: 20px;
	}
	h2{
		font-size: 30px;
		font-weight: 400;
		@include gradient-background();
		-webkit-background-clip: text;
	  -webkit-text-fill-color: transparent;
		@include media-breakpoint-down(sm){
			font-size: 28px;
		}
	}
	&.heading-section-white{
		.subheading{
			color: rgba(255,255,255,.9);
		}
		h2{
			font-size: 30px;
			background: $white;
			-webkit-background-clip: text;
		  -webkit-text-fill-color: transparent;
		}
		p{
			color: rgba(255,255,255,.9);
		}
	}
}

//COVER BG
.img,
.blog-img,
.user-img{
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center center;
}





//TESTIMONY
.testimony-section{
	position: relative;
	.owl-carousel{
		margin: 0;
	}
}
.testimony-wrap{
	display: block;
	position: relative;
	.user-img{
		width: 100px;
		height: 100px;
		border-radius: (50%);
		position: relative;
		margin-top: -75px;
		margin: 0 auto;
		.quote{
			position: absolute;
			bottom: -20px;
			left: 50%;
			width: 40px;
			height: 40px;
			transform: translateX(-50%);
			background: $primary;
			@include border-radius(50%);
			i{
				color: $white;
			}
		}
	}
	.name{
		font-weight: 500;
		font-size: 16px;
		margin-bottom: 0;
		color: $black;
	}
	.position{
		font-size: 13px;
	}
}




.about-image{
	@include media-breakpoint-down(sm){
		height: 400px;
		margin-bottom: 30px;
	}
}



.ftco-section{
	position: relative;
	width: 100%;
	display: block;
	.nav-link-wrap{
	}
	.nav-pills{
		p{
			margin-bottom: 0;
		}
		.nav-link{
			margin-bottom: 0;
			color: $primary;
			font-size: 18px;
			font-weight: 400;
			position: relative;
			display: inline-block;
			border-bottom: 1px solid $primary;
			margin-left: 5px;
			margin-right: 5px;
			padding:10px 15px;
			@include border-radius(0);
			&.active, &:hover{
				color: $white;
				@include transition(.3s);
				background: $primary;
				bordeR: 1px solid $primary;
				&:after{
					opacity: 1;
				}
				@include media-breakpoint-down(sm){
					&:after{
						opacity: 0;
					}
				}
			}
			@include media-breakpoint-down(sm){
				display: block;
				width: 100%;
				margin-bottom: 5px;
			}
		}
	}
	.tab-content{
		.tab-pane{
			h2{
				font-size: 24px;
			}
			.one-forth{
				width: 50%;
				img{
					-webkit-box-shadow: 0px 2px 5px 0px rgba(0,0,0,0.03);
					-moz-box-shadow: 0px 2px 5px 0px rgba(0,0,0,0.03);
					box-shadow: 0px 2px 5px 0px rgba(0,0,0,0.03);
					@include media-breakpoint-down(sm){
						margin-bottom: 20px;
					}
				}
				@include media-breakpoint-down(sm){
					width: 100%;
				}
			}
			.one-half{
				width: 50%;
				@include media-breakpoint-down(sm){
					width: 100%;
				}
			}
		}
	}
}

// magnific pop up

.image-popup {
	cursor: -webkit-zoom-in;
	cursor: -moz-zoom-in;
	cursor: zoom-in;
}
.mfp-with-zoom .mfp-container,
.mfp-with-zoom.mfp-bg {
  opacity: 0;
  -webkit-backface-visibility: hidden;
  -webkit-transition: all 0.3s ease-out; 
  -moz-transition: all 0.3s ease-out; 
  -o-transition: all 0.3s ease-out; 
  transition: all 0.3s ease-out;
}

.mfp-with-zoom.mfp-ready .mfp-container {
    opacity: 1;
}
.mfp-with-zoom.mfp-ready.mfp-bg {
    opacity: 0.8;
}

.mfp-with-zoom.mfp-removing .mfp-container, 
.mfp-with-zoom.mfp-removing.mfp-bg {
  opacity: 0;
}



//COUNTER
#section-counter{
	position: relative;
	z-index: 0;
	&:after{
		position: absolute;
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
		content: '';
		z-index: -1;
		opacity: .1;
		background: $black;
	}
}
.ftco-counter {
	padding: 0;
	@include media-breakpoint-down(lg){
		background-position: center center !important;
	}
	.icon{
		span{
		}
	}
	.text{
		strong.number{
			font-weight: 400;
			font-size: 34px;
			color: $white;
		}
		span{
			font-size: 16px;
			color: rgba(255,255,255,.7);
		}
		@include media-breakpoint-down(sm){
			text-align: center;
		}
	}
	.counter-wrap{
		@include media-breakpoint-down(sm){
			margin-bottom: 20px;
		}
	}
}

//PRICING
.pricing-entry{
	border: 1px solid lighten($black,94%);
	position: relative;
	@include transition(.3s);
	@include media-breakpoint-down(sm){
		margin-bottom: 4em;
	}
	>div{
		padding: 20px;
		p{
			margin-bottom: 0;
		}
	}
	h3{
		font-size: 14px;
		text-transform: uppercase;
	}
	p{
		.price{
			font-weight: 400;
			font-size: 26px;
			color: $primary;
		}
		.per{
			font-size: 12px;
		}
	}
	ul{
		margin: 0;
		padding: 0;
		li{
			list-style: none;
			padding: 10px 20px;
			&:nth-child(odd){
				background: lighten($black,98%);
			}
		}
	}
	.button{
		position: absolute;
		bottom: -26px;
		left: 0;
		right: 0;
		margin-bottom: 0;
		opacity: 0;
		@include transition(.3s);
		.btn{
			@include border-radius(30px);
			&.btn-outline-primary{
				background: $white;
				border: 1px solid lighten($black,94%);
				color: lighten($black,80%);
				&:hover, &:focus{
					background: $white !important;
				}
			}
		}
	}
	&:hover, &:focus{
		border: 1px solid $primary;
		.button{
			opacity: 1;
			.btn{
				&.btn-outline-primary{
					background: $primary !important;
					border: 1px solid $primary !important;
					color: $white;
				}
			}
		}
	}
}

//blocks 
.block-20 {
	overflow: hidden;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center center;
	height: 350px;
	position: relative;
	display: block;
}
.blog-entry{
	@include media-breakpoint-up(md){
		margin-bottom: 30px;
	}
	@include media-breakpoint-down(sm){
		margin-bottom: 30px;
	}
	.text {
		position: relative;
		border-top: 0;
		border-radius: 2px;
		.desc{
			width: calc(100% - 100px);
		}
		.heading {
			font-size: 20px;
			margin-bottom: 16px;
			a {
				color: $black;
				&:hover, &:focus, &:active {
					color: $primary;
				}
			}
		}
		.meta-chat{
			color: lighten($black, 70%);
		}
		.read{
			color: $black;
			font-size: 14px;
		}
	}
	.meta {
		width: 100px;
		text-align: right;
		> div {
			display: block;
			margin-right: 5px;
			margin-bottom: 0px;
			font-size: 15px;
			a {
				color: lighten($black, 70%);
				font-size: 13px;
				&:hover {
					color: lighten($black, 80%);
				}
			}
		}
	}
}


.block-23 {
	ul {
		padding: 0;
		li {
			
			&, > a {
				display: table;
				line-height: 1.5;
				margin-bottom: 15px;
			}
			span{
				color: rgba($white, .7);
			}
			.icon, .text {
				display: table-cell;
				vertical-align: top;
			}
			.icon {
				width: 40px;
				font-size: 18px;
				padding-top: 2px;
				color: rgba($white, 1);
			}
			
		}
	}
}

.block-6 {
	margin-bottom: 40px;
	.icon {
		span {
			&:before {
			}
		}
	}
	.media-body {
		.heading {

		}
		p {
			font-size: 16px;
		}
	}
} 

//### .block-10 
.block-10 {
	.chef-img{
		height: 400px;
	}
	.person-info {
		height: 75px;
		span {
			display: block;
		}
		.name {
			font-size: 20px;
			color: $black;
			font-weight: 400;
		}
		.position {
			font-size: 14px;
		}
	}
}

.block-17 {
	form {
		.fields {
			// width: calc(100% - 140px);
			width: 100%;
			position: relative;
			@include media-breakpoint-down(md) {
				width: 100%;
			}
			.one-third {
				width: 100% !important;
				margin-bottom: 10px;
				@include media-breakpoint-down(md) {
					width: 100%;
					border-right: none;
					padding-left: 0;
					padding-right: 0;
					margin-bottom: 10px;
				}
				&:first-child {
					// padding-left: 0;
				}
				&:last-child {
					border-right: none;
				}
				label{
					font-weight: 700;
					color: $black;
				}
			}
			.form-control {
				box-shadow: none!important;
				border: transparent;
				background: transparent !important;
				color: lighten($black,30%) !important;
				border: 2px solid rgba(0,0,0,.1);
				font-size: 14px;
				width: 100%;
				@include border-radius(4px);
				&::-webkit-input-placeholder { /* Chrome/Opera/Safari */
				  color: lighten($black,30%);
				}
				&::-moz-placeholder { /* Firefox 19+ */
				  color: lighten($black,30%);
				}
				&:-ms-input-placeholder { /* IE 10+ */
				  color: lighten($black,30%);
				}
				&:-moz-placeholder { /* Firefox 18- */
				  color: lighten($black,30%);
				}
			}
			.icon {
				position: absolute;
				top: 50%;
				right: 30px;
				font-size: 14px;
				transform: translateY(-50%);
				color: rgba($black,.7);
				@include media-breakpoint-down(sm) {
					right: 10px;
				}
			}
			.textfield-search, .select-wrap {
			}
			.textfield-search {
				input {

				}
			}
			.select-wrap {
				position: relative;
				select {
					appearance: none;
				}
			}
		}
		.search-submit {
			width: 100%;
			background: $primary;
			border: 2px solid $primary;
			color: $white;
			padding: 12px 10px;
			@include border-radius(4px);
			&:hover{
				background: $black;
				color: $white;
				border: 2px solid $black;
			}
		}
	}
}

//### .block-18 
.block-18 {
	width: 100%;
	display: block;
	.icon, .text {
	}
	.icon {
		> span {
			font-size: 40px;
		}
	}
	.text {
		strong {
			font-size: 30px;
		}
		span {
			display: block;
		}
	}
}


.block-27 {
	ul {
		padding: 0;
		margin: 0;
		li {
			display: inline-block;
			margin-bottom: 4px;
			font-weight: 400;
			a,span {
				color: $primary;
				text-align: center;
				display: inline-block;
				width: 40px;
				height: 40px;
				line-height: 40px;
				border-radius: 50%;
				border: 1px solid lighten($primary,25%);
			}
			&.active {
				a, span {
					background: lighten($primary,25%);
					color: $primary;
					border: 1px solid transparent;
				}
			}
		}
	}
}


// .block-7 {
// 	margin-bottom: 30px;
// 	padding: 30px;
// 	-webkit-box-shadow: 0px 2px 5px 0px rgba(0,0,0,0.03);
// 	-moz-box-shadow: 0px 2px 5px 0px rgba(0,0,0,0.03);
// 	box-shadow: 0px 2px 5px 0px rgba(0,0,0,0.03);
// 	background: $white;
// 	.heading {
// 		font-size: 14px;
// 		line-height: 1;
// 		margin: 0;
// 		padding: 0;
// 		margin-bottom: 10px;
// 		display: inline-block;
// 		text-transform: uppercase;
// 	}
// 	.heading-2 {
// 		font-size: 16px;
// 	}
// 	.price {
// 		margin: 0;
// 		padding: 0;
// 		display: block;
// 		sup {
// 			font-size: 20px;
// 			top: -.7em; 
// 			color: $primary;
// 		}
// 		.number {
// 			font-size: 40px;
// 			font-weight: 500;
// 			color: $primary;
// 		}
// 	}
// 	.excerpt {
// 		margin-bottom: 20px;
// 		color: lighten($black,50%);
// 	}
// 	.label2 {
// 		text-transform: uppercase;
// 	}
// 	.pricing-text {
// 		margin-bottom: 0;
// 		&, li {
// 			padding: 0;
// 			margin: 0;
// 		}
// 		li {
// 			list-style: none;
// 			margin-bottom: 15px;
// 			color: darken(#ccc, 10%);
// 			strong {
// 				color: $black;
// 			}
// 		}
// 	}

// }

.block-8 {
	.accordion-item {
		.heading {
			font-size: 16px;
			font-weight: 400;
			padding: 10px 0;
			> a {
				padding-left: 35px;
				position: relative;
				color: $black;
				&:before {

					width: 20px;
					height: 20px;
					line-height: 18px;
					border: 1px solid #ccc;
					text-align: center;
					font-size: 18px;
					top: .1em;
					left: 0;
				}
				&[aria-expanded="true"] {
					&:before {
						font-family: 'icomoon';
						position: absolute;
						content: "\e316";
						transition: .3s all ease;
						background: $primary;
						color: $white;
						border: 1px solid $primary;
					}
				}
				&[aria-expanded="false"] {
					&:before {
						content: "\e313";
						color: #ccc;	
						font-family: 'icomoon';
						position: absolute;
				
						transition: .3s all ease;
					}
				}
				
			}
		}
		.body-text {
			font-size: 16px;
			padding: 5px 0;
			padding-left: 30px;
		}
	}
}

//### .block-4
.block-4 {
	.nonloop {
		.owl-stage {
			padding-bottom: 2em;
		}
		.owl-item {
			box-shadow: 0 7px 20px -5px rgba(0,0,0,.2);	
		}
		.owl-nav {
			z-index: 2;
			position: absolute;
			width: 100%;
			bottom: -2px;
			.owl-prev, .owl-next {
				opacity: .2;
				transition: .3s all ease;
				&:hover {
					opacity: 1;
				}
				&.disabled {
					display: none;
				}
				position: absolute;
				span {
					font-size: 30px;
				}
			}
			.owl-prev {
				left: 50px;

			}
			.owl-next {
				right: 50px;
			}
		}
		.owl-dots {
			bottom: -40px;
			position: absolute;
			width: 100%;
			text-align: center;
			.owl-dot {
				display: inline-block;
				width: 8px;
				height: 8px;
				background: #ccc;
				border-radius: 50%;
				margin-right: 10px;
				margin-bottom: 10px;
				transition: .3s all ease;
				&.active {
					
					background: $primary;
				}
			}
		}
	}
}

.contact-section {
	.contact-info{
		p{
			a{
				color: lighten($black,10%);
			}
			span{}
		}
	}
}
.block-9 {

	.form-control {
		outline: none!important;
		box-shadow: none!important;
		font-size: 15px;
	}
	#map {
	}
}


//### .block-21
.block-21 {
	.blog-img{
		display: block;
		height: 80px;
		width: 80px;
	}
	.text {
		width: calc(100% - 100px);
		.heading {
			font-size: 18px;
			font-weight: 300;
			a {
				color: $black;
				&:hover, &:active, &:focus {
					color: $primary;
				}
			}
		}
		.meta {
			> div {
				display: inline-block;
				font-size: 12px;
				margin-right: 5px;
				a {
					color: lighten($black, 50%);
				}
			}
		}
	}
}

.custom-pagination {
	width: 100%;
	text-align: center;
	display: inline-block;
	li {
		display: inline-block;
	}
	.prev, .next {
		a {
			font-size: 20px!important;
			line-height: 38px!important;
		}
	}
	li, .prev, .next {
		a {
			width: 40px;
			height: 40px;
			line-height: 40px;
			padding: 0;
			margin: 0;
			border-radius: 50%!important;
			font-size: 16px;
		}
		&.active {
			a {
				display: block;
				width: 40px;
				height: 40px;
				line-height: 40px;
				padding: 0;
				margin: 0;
				border-radius: 50%!important;
				font-size: 16px;
				background: $primary;
				color: $white;
				&:hover, &:focus {
					color: $white;
				}
			}	
		}
	}
	.prev {
		float: left;
	}
	.next {
		float: right;
	}
}

/* Blog*/
.post-info {
	font-size: 12px;
	text-transform: uppercase;
	font-weight: bold;
	color: $white;
	letter-spacing: .1em;
	> div {
		display: inline-block;

		.seperator {
			display: inline-block;
			margin: 0 10px;
			opacity: .5;
		}
	}
}

.tagcloud {
	a {
		text-transform: uppercase;
		display: inline-block;
		padding: 4px 10px;
		margin-bottom: 7px;
		margin-right: 4px;
		border-radius: 4px;
		color: $black;
		border: 1px solid #ccc;
		font-size :11px;
		&:hover {
			border: 1px solid #000;
		}
	}
}

.comment-form-wrap {
	clear: both;
}

.comment-list {
	padding: 0;
	margin: 0;
	.children {
		padding: 50px 0 0 40px;
		margin: 0;
		float: left;
		width: 100%;
	}
	li {
		padding: 0;
		margin: 0 0 30px 0;
		float: left;
		width: 100%;
		clear: both;
		list-style: none;
		.vcard {
			width: 80px;
			float: left;
			img {
				width: 50px;
				border-radius: 50%;
			}
		}
		.comment-body {
			float: right;
			width: calc(100% - 80px);
			h3 {
				font-size: 20px;
			}
			.meta {
				font-size: 13px;
				color: #ccc;
			}
			.reply {
				padding: 5px 10px;
				background: lighten($black, 95%);
				color: $black;
				text-transform: uppercase;
				font-size: 11px;
				letter-spacing: .1em;
				font-weight: 400;
				border-radius: 4px;
				&:hover {
					color: $white;
					background: lighten($black, 0%);
				}
			}
		}
	}
}

.search-form {
	.form-group {
		position: relative;
		input {
			padding-right: 50px;
		}
	}
	.icon {
		position: absolute;
		top: 50%;
		right: 20px;
		transform: translateY(-50%);
	}
}

.subscribe-form{
	.form-group {
		position: relative;
		margin-bottom: 0;
		border: 1px solid rgba(255,255,255,.7);
		@include border-radius(30px);
		input {
			background: transparent !important;
			border: 1px solid transparent;
			color: rgba(255,255,255,.7) !important;
			font-size: 16px;
			padding-left: 20px;
			padding-right: 20px;
			@include border-radius(30px);
			&::-webkit-input-placeholder { /* Chrome/Opera/Safari */
			  color: rgba(255,255,255,.7) !important;
			}
			&::-moz-placeholder { /* Firefox 19+ */
			  color: rgba(255,255,255,.7) !important;
			}
			&:-ms-input-placeholder { /* IE 10+ */
			  color: rgba(255,255,255,.7) !important;
			}
			&:-moz-placeholder { /* Firefox 18- */
			  color: rgba(255,255,255,.7) !important;
			}
		}
		.submit{
			border-left: 1px solid rgba(255,255,255,.7);
			color: $white !important;
			@include border-radius(0);
			font-size: 16px;
			// position: absolute;
			// top: 50%;
			// right: 0;
			// transform: translateY(-50%);
			&:hover{
				cursor: pointer;
			}
		}
	}
	.icon {
		position: absolute;
		top: 50%;
		right: 20px;
		transform: translateY(-50%);
		color: rgba(255,255,255,.8);
	}
}

// sidebar
.sidebar-box {
	margin-bottom: 30px;
	padding: 25px;
	font-size: 15px;
	width: 100%;
	
	float: left;
	
	background: $white;
	*:last-child {
		margin-bottom: 0;
	}
	h3 {
		font-size: 18px;
		margin-bottom: 15px;
	}
}

.categories, .sidelink {
	li {
		position: relative;
		margin-bottom: 10px;
		padding-bottom: 10px;
		border-bottom: 1px dotted gray('300');
		list-style: none;
		&:last-child {
			margin-bottom: 0;
			border-bottom: none;
			padding-bottom: 0;
		}
		a {
			display: block;
			span {
				position: absolute;
				right: 0;
				top: 0;
				color: #ccc;
			}
		}
		&.active {
			a {
				color: $black;
				font-style: italic;
			}
		}
	}
}



#ftco-loader {
	position: fixed;
	width:  96px;
	height: 96px;
	left:  50%;
	top: 50%;
	transform: translate(-50%, -50%);
	background-color: rgba(255,255,255,0.9);
	box-shadow: 0px 24px 64px rgba(0,0,0,0.24);
	border-radius:16px;
	opacity: 0; 
	visibility: hidden;
	transition: opacity .2s ease-out, visibility 0s linear .2s;
	z-index:1000;
}

#ftco-loader.fullscreen {
	padding:  0;
	left:  0;
	top:  0;
	width:  100%;
	height: 100%;
	transform: none;
	background-color: #fff;
	border-radius: 0;
	box-shadow: none;
}

#ftco-loader.show {
	transition: opacity .4s ease-out, visibility 0s linear 0s;
	visibility: visible;
	opacity: 1;
}

#ftco-loader .circular {
  animation: loader-rotate 2s linear infinite;
  position: absolute;
  left:  calc(50% - 24px);
  top:  calc(50% - 24px);
  display: block;
  transform: rotate(0deg);
}

#ftco-loader .path {
  stroke-dasharray: 1, 200;
  stroke-dashoffset: 0;
  animation: loader-dash 1.5s ease-in-out infinite;
  stroke-linecap: round;
}

@keyframes loader-rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loader-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35px;
  }
  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -136px;
  }
}