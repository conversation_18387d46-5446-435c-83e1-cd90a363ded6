@model IEnumerable<MyMvcApp.Models.ServiceCategory>

@{
    ViewData["Title"] = "Quản lý danh mục dịch vụ";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@section Styles {
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="~/css/admin.css" rel="stylesheet">
}


    
        <!-- Sidebar -->
        

        <!-- Main content -->
        
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-tags me-2"></i>
                    <PERSON><PERSON><PERSON>n lý danh mục dịch vụ
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                        <i class="fas fa-plus me-2"></i>
                        Thêm danh mục mới
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="categoryTable">
                            <thead>
                                <tr>
                                    <th>Mã</th>
                                    <th>Tên danh mục</th>
                                    <th>Mô tả</th>
                                    <th>Trạng thái</th>
                                    <th>Ngày tạo</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>@item.Id</td>
                                        <td>@item.Name</td>
                                        <td>@(item.Description?.Length > 50 ? item.Description.Substring(0, 50) + "..." : item.Description)</td>
                                        <td>
                                            <span class="badge @(item.IsActive ? "bg-success" : "bg-danger")">
                                                @(item.IsActive ? "Đang hoạt động" : "Ngừng hoạt động")
                                            </span>
                                        </td>
                                        <td>@item.CreatedAt.ToString("dd/MM/yyyy")</td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="editCategory(@item.Id)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCategory(@item.Id)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm danh mục mới</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCategoryForm">
                    @Html.AntiForgeryToken()
                    <div class="mb-3">
                        <label class="form-label">Tên danh mục</label>
                        <input type="text" class="form-control" name="Name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Mô tả</label>
                        <textarea class="form-control" name="Description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" onclick="saveCategory()">Lưu</button>
            </div>
        </div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Chỉnh sửa danh mục</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editCategoryForm">
                    @Html.AntiForgeryToken()
                    <input type="hidden" name="Id">
                    <div class="mb-3">
                        <label class="form-label">Tên danh mục</label>
                        <input type="text" class="form-control" name="Name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Mô tả</label>
                        <textarea class="form-control" name="Description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="IsActive" id="isActiveCategory">
                            <label class="form-check-label" for="isActiveCategory">Đang hoạt động</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" onclick="updateCategory()">Cập nhật</button>
            </div>
        </div>

@section Scripts {
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#categoryTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json'
                },
                order: [[0, 'desc']],
                pageLength: 25,
                responsive: true
            });
        });

        function saveCategory() {
            var formData = $('#addCategoryForm').serialize();
            $.ajax({
                url: '/Service/CreateServiceCategory',
                type: 'POST',
                data: formData,
                success: function (response) {
                    if (response.success) {
                        $('#addCategoryModal').modal('hide');
                        location.reload();
                    } else {
                        alert(response.message || 'Có lỗi xảy ra');
                    }
                },
                error: function () {
                    alert('Có lỗi xảy ra khi thực hiện yêu cầu');
                }
            });
        }

        function editCategory(id) {
            $.get('/Service/GetServiceCategory', { id: id })
                .done(function (response) {
                    if (response.success) {
                        var form = document.getElementById('editCategoryForm');
                        form.Id.value = response.id;
                        form.Name.value = response.name;
                        form.Description.value = response.description;
                        form.IsActive.checked = response.isActive;
                        $('#editCategoryModal').modal('show');
                    } else {
                        alert(response.message || 'Có lỗi xảy ra');
                    }
                })
                .fail(function () {
                    alert('Có lỗi xảy ra khi thực hiện yêu cầu');
                });
        }

        function updateCategory() {
            var formData = new FormData(document.getElementById('editCategoryForm'));

            // Debug: Log form data
            console.log('Form data being sent:');
            for (var pair of formData.entries()) {
                console.log(pair[0] + ': ' + pair[1]);
            }

            // Handle checkbox value properly
            var isActiveCheckbox = document.getElementById('isActiveCategory');
            if (isActiveCheckbox.checked) {
                formData.set('isActive', 'true');
            } else {
                formData.set('isActive', 'false');
            }

            $.ajax({
                url: '/Service/UpdateServiceCategory',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    console.log('Response:', response);
                    if (response.success) {
                        $('#editCategoryModal').modal('hide');
                        location.reload();
                    } else {
                        var errorMessage = response.message || 'Có lỗi xảy ra';
                        if (response.errors && response.errors.length > 0) {
                            errorMessage += '\n\nChi tiết lỗi:\n' + response.errors.join('\n');
                        }
                        alert(errorMessage);
                    }
                },
                error: function (xhr, status, error) {
                    console.log('Error details:', xhr.responseText);
                    alert('Có lỗi xảy ra khi thực hiện yêu cầu: ' + error);
                }
            });
        }

        function deleteCategory(id) {
            if (confirm('Bạn có chắc chắn muốn xóa danh mục này?')) {
                var token = $('input[name="__RequestVerificationToken"]').val();
                $.post('/Service/DeleteServiceCategory', {
                    id: id,
                    __RequestVerificationToken: token
                })
                    .done(function (response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.message || 'Có lỗi xảy ra');
                        }
                    })
                    .fail(function () {
                        alert('Có lỗi xảy ra khi thực hiện yêu cầu');
                    });
            }
        }
    </script>
}
