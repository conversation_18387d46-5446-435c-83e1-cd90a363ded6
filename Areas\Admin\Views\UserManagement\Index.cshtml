@model IEnumerable<MyMvcApp.ViewModels.Admin.UserManagementViewModel>

@{
    ViewData["Title"] = "Quản lý người dùng";
}

@section Styles {
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="~/css/admin.css" rel="stylesheet">
}


    
        <!-- Sidebar -->
        

        <!-- Main content -->
        
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Quản lý người dùng</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a asp-controller="UserManagement" asp-action="Create" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Thêm người dùng mới
                    </a>
                </div>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="userTable">
                            <thead>
                                <tr>
                                    <th>Họ tên</th>
                                    <th>Email</th>
                                    <th>Vai trò</th>
                                    <th>Ngày tạo</th>
                                    <th>Trạng thái</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var user in Model)
                                {
                                    <tr>
                                        <td>@user.FullName</td>
                                        <td>@user.Email</td>
                                        <td>
                                            @foreach (var role in user.RoleNames)
                                            {
                                                <span class="badge bg-info">@role</span>
                                            }
                                        </td>
                                        <td>@user.CreatedAt.ToString("dd/MM/yyyy")</td>
                                        <td>
                                            <span class="badge @(user.IsActive ? "bg-success" : "bg-danger")">
                                                @(user.IsActive ? "Đang hoạt động" : "Đã khóa")
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-controller="UserManagement" asp-action="Edit" asp-route-id="@user.Id" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm @(user.IsActive ? "btn-warning" : "btn-success")"
                                                        onclick="toggleUserStatus('@user.Id')">
                                                    <i class="fas @(user.IsActive ? "fa-lock" : "fa-unlock")"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger"
                                                        onclick="deleteUser('@user.Id')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        

@section Scripts {
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#userTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json'
                },
                order: [[3, 'desc']],
                pageLength: 25,
                responsive: true
            });
        });

        function toggleUserStatus(userId) {
            if (confirm('Bạn có chắc chắn muốn thay đổi trạng thái người dùng này?')) {
                $.post('@Url.Action("ToggleStatus", "UserManagement")', { id: userId })
                    .done(function (response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.message || 'Có lỗi xảy ra');
                        }
                    })
                    .fail(function () {
                        alert('Có lỗi xảy ra khi thực hiện yêu cầu');
                    });
            }
        }

        function deleteUser(userId) {
            if (confirm('Bạn có chắc chắn muốn xóa người dùng này?')) {
                $.post('@Url.Action("Delete", "UserManagement")', { id: userId })
                    .done(function (response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.message || 'Có lỗi xảy ra');
                        }
                    })
                    .fail(function () {
                        alert('Có lỗi xảy ra khi thực hiện yêu cầu');
                    });
            }
        }
    </script>
}
