// ===== MODERN DENTAL CLINIC UI EFFECTS =====

document.addEventListener('DOMContentLoaded', function() {

    // Initialize only essential effects (disabled problematic ones)
    initScrollReveal(); // Call to cleanup existing scroll-reveal elements
    // initParallaxEffect(); // Disabled - causes performance issues
    initSmoothScrolling();
    initCardHoverEffects(); // Call to cleanup existing card hover effects
    // initLoadingAnimations(); // Disabled - causes table loading jitter
    initTooltips();
    // initCounterAnimations(); // Disabled - can cause jitter
    // initTypewriterEffect(); // Disabled - not needed

    // Force cleanup any remaining inline styles
    setTimeout(() => {
        forceCleanupAllAnimations();
    }, 100);

    console.log('🦷 Essential UI Effects Loaded (Optimized)');
});

// Force cleanup all animation-related inline styles
function forceCleanupAllAnimations() {
    // Remove all problematic inline styles from all elements
    document.querySelectorAll('*[style*="animation"], *[style*="transform"], *[style*="transition"]').forEach(el => {
        // Keep only essential styles, remove animation-related ones
        const style = el.getAttribute('style');
        if (style) {
            const cleanStyle = style
                .replace(/animation[^;]*;?/gi, '')
                .replace(/animation-delay[^;]*;?/gi, '')
                .replace(/transform[^;]*;?/gi, '')
                .replace(/transition[^;]*;?/gi, '')
                .replace(/box-shadow[^;]*rgba\(46,\s*139,\s*139[^;]*;?/gi, '') // Remove specific box-shadow
                .replace(/;;+/g, ';') // Clean up double semicolons
                .replace(/^;|;$/g, ''); // Clean up leading/trailing semicolons

            if (cleanStyle.trim()) {
                el.setAttribute('style', cleanStyle);
            } else {
                el.removeAttribute('style');
            }
        }
    });

    // Specifically target scroll-reveal elements
    document.querySelectorAll('.scroll-reveal, .revealed').forEach(el => {
        el.classList.remove('scroll-reveal', 'revealed');
        el.removeAttribute('style');
    });

    console.log('🧹 Force cleanup completed - all animations removed');
}

// ===== SCROLL REVEAL ANIMATION ===== DISABLED
function initScrollReveal() {
    // COMPLETELY DISABLED - This function was causing screen jitter
    // Remove all scroll-reveal classes and inline styles
    document.querySelectorAll('.scroll-reveal').forEach(el => {
        el.classList.remove('scroll-reveal', 'revealed');
        el.style.animationDelay = '';
        el.style.transform = '';
        el.style.boxShadow = '';
        el.style.opacity = '';
    });

    // Remove scroll-reveal from cards and statistics
    document.querySelectorAll('.card, .statistics-card').forEach(el => {
        el.classList.remove('scroll-reveal', 'revealed');
        el.style.animationDelay = '';
        el.style.transform = '';
        el.style.boxShadow = '';
        el.style.opacity = '';
    });

    console.log('🚫 Scroll reveal animations disabled');
}

// ===== PARALLAX EFFECT =====
function initParallaxEffect() {
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.parallax');
        
        parallaxElements.forEach(element => {
            const speed = element.dataset.speed || 0.5;
            const yPos = -(scrolled * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
    });
}

// ===== SMOOTH SCROLLING =====
function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// ===== CARD HOVER EFFECTS ===== DISABLED
function initCardHoverEffects() {
    // COMPLETELY DISABLED - Remove all inline styles from cards
    document.querySelectorAll('.card').forEach(card => {
        // Remove any existing inline styles
        card.style.transform = '';
        card.style.boxShadow = '';
        card.style.transition = '';

        // Remove any existing event listeners by cloning the element
        const newCard = card.cloneNode(true);
        card.parentNode.replaceChild(newCard, card);
    });

    // Clean up statistics cards
    document.querySelectorAll('.border-left-primary, .border-left-success, .border-left-info, .border-left-warning, .border-left-danger').forEach(card => {
        card.style.transform = '';
        card.style.boxShadow = '';
        card.style.transition = '';

        // Remove any existing event listeners
        const newCard = card.cloneNode(true);
        card.parentNode.replaceChild(newCard, card);
    });

    console.log('🚫 Card hover effects disabled and cleaned up');
}

// ===== LOADING ANIMATIONS =====
function initLoadingAnimations() {
    // Add loading shimmer to tables while they load
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        if (table.querySelector('tbody').children.length === 0) {
            table.classList.add('loading-shimmer');
        }
    });

    // Remove loading states after content loads
    setTimeout(() => {
        document.querySelectorAll('.loading-shimmer').forEach(el => {
            el.classList.remove('loading-shimmer');
        });
    }, 1000);
}

// ===== TOOLTIPS =====
function initTooltips() {
    // Initialize Bootstrap tooltips if available
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // Custom tooltip for buttons
    document.querySelectorAll('.btn').forEach(btn => {
        if (!btn.hasAttribute('title')) return;
        
        btn.addEventListener('mouseenter', function(e) {
            const tooltip = document.createElement('div');
            tooltip.className = 'custom-tooltip';
            tooltip.textContent = this.getAttribute('title');
            tooltip.style.cssText = `
                position: absolute;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 12px;
                z-index: 1000;
                pointer-events: none;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;
            
            document.body.appendChild(tooltip);
            
            const rect = this.getBoundingClientRect();
            tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
            tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
            
            setTimeout(() => tooltip.style.opacity = '1', 10);
            
            this.addEventListener('mouseleave', () => {
                tooltip.remove();
            }, { once: true });
        });
    });
}

// ===== COUNTER ANIMATIONS =====
function initCounterAnimations() {
    const counters = document.querySelectorAll('.h5, .statistics-number');
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.textContent.replace(/[^\d]/g, ''));
        if (isNaN(target)) return;
        
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current).toLocaleString();
        }, 16);
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    });

    counters.forEach(counter => {
        observer.observe(counter);
    });
}

// ===== TYPEWRITER EFFECT =====
function initTypewriterEffect() {
    const typewriterElements = document.querySelectorAll('.typewriter');
    
    typewriterElements.forEach(element => {
        const text = element.textContent;
        element.textContent = '';
        element.style.borderRight = '2px solid #2E8B8B';
        
        let i = 0;
        const timer = setInterval(() => {
            element.textContent += text.charAt(i);
            i++;
            if (i >= text.length) {
                clearInterval(timer);
                // Blinking cursor effect
                setInterval(() => {
                    element.style.borderRight = element.style.borderRight === '2px solid transparent' 
                        ? '2px solid #2E8B8B' 
                        : '2px solid transparent';
                }, 500);
            }
        }, 100);
    });
}

// ===== UTILITY FUNCTIONS =====

// Add stagger animation to elements
function addStaggerAnimation(selector, delay = 100) {
    document.querySelectorAll(selector).forEach((el, index) => {
        el.style.animationDelay = `${index * delay}ms`;
        el.classList.add('animate-fade-in-up');
    });
}

// Add floating animation to elements
function addFloatingAnimation(selector) {
    document.querySelectorAll(selector).forEach(el => {
        el.classList.add('floating');
    });
}

// Smooth page transitions - DISABLED to prevent jitter
function initPageTransitions() {
    // Disabled - causes screen jitter on page load
    // const main = document.querySelector('main');
    // if (main) {
    //     main.classList.add('animate-fade-in-up');
    // }
}

// Initialize page transitions - DISABLED
// initPageTransitions();

// ===== RESPONSIVE UTILITIES =====
function handleResponsiveAnimations() {
    const isMobile = window.innerWidth <= 768;
    
    if (isMobile) {
        // Reduce animations on mobile for better performance
        document.querySelectorAll('.card').forEach(card => {
            card.style.transition = 'transform 0.2s ease';
        });
    }
}

window.addEventListener('resize', handleResponsiveAnimations);
handleResponsiveAnimations();

// ===== ACCESSIBILITY =====
// Respect user's motion preferences
if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    document.documentElement.style.setProperty('--animation-duration', '0.01ms');
    document.documentElement.style.setProperty('--transition-duration', '0.01ms');
}
