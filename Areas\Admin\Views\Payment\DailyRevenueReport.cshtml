@model IEnumerable<MyMvcApp.Models.PaymentTransaction>

@{
    ViewData["Title"] = "Báo cáo doanh thu ngày";
    DateTime reportDate = ViewBag.ReportDate;
    decimal totalRevenue = ViewBag.TotalRevenue;
}

@section Styles {
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="~/css/admin.css" rel="stylesheet">
    <style>
        .form-group label {
            font-weight: bold;
        }
    </style>
}


    
        <!-- Sidebar -->
        

        <!-- Main content -->
        
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-chart-line"></i> @ViewData["Title"]
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a asp-action="Index" asp-controller="Payment" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Quay lại danh sách
                    </a>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    Chọn ngày báo cáo
                </div>
                <div class="card-body">
                    <form method="get" asp-action="DailyRevenueReport">
                        <div class="row align-items-end">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="reportDate">Ngày</label>
                                    <input type="date" id="reportDate" name="reportDate" class="form-control"
                                           value="@reportDate.ToString("yyyy-MM-dd")" />
                                </div>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary"><i class="fas fa-filter"></i> Lọc</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    Kết quả báo cáo cho ngày: @reportDate.ToString("dd/MM/yyyy")
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <strong>Tổng doanh thu: <span class="fs-5">@totalRevenue.ToString("N0") VNĐ</span></strong>
                    </div>

                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="revenueTable">
                                <thead>
                                    <tr>
                                        <th>Mã giao dịch</th>
                                        <th>Lịch hẹn ID</th>
                                        <th>Bệnh nhân</th>
                                        <th>Dịch vụ</th>
                                        <th>Số tiền</th>
                                        <th>Phương thức</th>
                                        <th>Thời gian TT</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var transaction in Model)
                                    {
                                        <tr>
                                            <td>@transaction.OrderId</td>
                                            <td>@transaction.AppointmentId</td>
                                            <td>@(transaction.Appointment?.Patient?.FullName ?? "N/A")</td>
                                            <td>@(transaction.Appointment?.Service?.Name ?? "N/A")</td>
                                            <td>@transaction.Amount.ToString("N0") VNĐ</td>
                                            <td>@transaction.PaymentMethod</td>
                                            <td>@transaction.CompletedAt?.ToString("dd/MM/yyyy HH:mm")</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-info-circle fa-2x text-muted mb-2"></i>
                            <p>Không có giao dịch nào được ghi nhận trong ngày này.</p>
                        </div>
                    }
                </div>
            </div>
        

@section Scripts {
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#revenueTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/vi.json"
                },
                "order": [[6, "desc"]]
            });
        });
    </script>
}
